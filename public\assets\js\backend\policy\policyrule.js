define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'policy/policyrule/index' + location.search,
                    add_url: 'policy/policyrule/add',
                    edit_url: 'policy/policyrule/edit',
                    del_url: 'policy/policyrule/del',
                    multi_url: 'policy/policyrule/multi',
                    testmatch_url: 'policy/policyrule/testmatch',
                    getquestions_url: 'policy/policyrule/getquestions',
                    table: 'policy_rule',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weight',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: 'ID', sortable: true},
                        {field: 'questionnaire_title', title: '所属问卷', operate: 'LIKE', formatter: function(value, row, index) {
                            console.log('questionnaire_title:', value, 'row:', row);
                            return value || '未关联问卷';
                        }},
                        {field: 'name', title: '规则名称', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'description', title: '规则描述', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'condition_description', title: '条件组合', operate: false, formatter: function(value, row, index) {
                            console.log('condition_description:', value);
                            if (!value || value === '无条件') {
                                return '<span class="text-muted">无条件</span>';
                            }
                            var shortText = value.length > 30 ? value.substring(0, 30) + '...' : value;
                            return '<a href="javascript:;" class="btn-view-condition" data-id="' + row.id + '" data-content="' + Fast.api.escape(value) + '">' + shortText + '</a>';
                        }},
                        {field: 'policy_names', title: '关联政策', operate: false, formatter: function(value, row, index) {
                            if (!value || value === '无关联政策') {
                                return '<span class="text-muted">无关联政策</span>';
                            }
                            var shortText = value.length > 30 ? value.substring(0, 30) + '...' : value;
                            return '<a href="javascript:;" class="btn-view-policy" data-id="' + row.id + '" data-content="' + Fast.api.escape(value) + '">' + shortText + '</a>';
                        }},
                        {field: 'condition_count', title: '条件数量', operate: false},
                        {field: 'weight', title: '权重', operate: false, sortable: true},
                        {field: 'sort', title: '排序', operate: false, sortable: true},
                        {
                            field: 'status',
                            title: '状态',
                            searchList: {"normal":"正常","hidden":"隐藏"},
                            formatter: Table.api.formatter.status
                        },
                        {field: 'createtime', title: '创建时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime, sortable: true},
                        {field: 'operate', title: '操作', table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定条件组合查看事件
            $(document).on('click', '.btn-view-condition', function() {
                var content = $(this).data('content');
                Layer.alert(content, {
                    title: '条件组合详情',
                    area: ['600px', '400px'],
                    scrollbar: false
                });
            });

            // 绑定关联政策查看事件
            $(document).on('click', '.btn-view-policy', function() {
                var content = $(this).data('content');
                Layer.alert(content, {
                    title: '关联政策详情',
                    area: ['600px', '400px'],
                    scrollbar: false
                });
            });

            // 测试匹配
            $(document).on('click', '.btn-test', function () {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Toastr.error('请选择至少一条记录');
                    return false;
                }
                if (ids.length > 1) {
                    Toastr.error('只能选择一个规则进行测试');
                    return false;
                }
                
                Fast.api.open('policy/policyrule/testmatch?rule_id=' + ids[0], '测试规则匹配', {
                    area: ['80%', '90%']
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
            // 初始化时如果已选择问卷，加载问题
            var questionnaireId = $('select[name="row[questionnaire_id]"]').val();
            if (questionnaireId) {
                Controller.api.loadQuestions(questionnaireId);
            } else {
                // 添加一个空条件
                Controller.api.addCondition();
            }
        },
        edit: function () {
            Controller.api.bindevent();

            // 调试信息
            console.log('Edit mode - existingConditions:', window.existingConditions);
            console.log('Edit mode - questionsData:', window.questionsData);

            // 编辑模式下加载现有条件
            var questionnaireId = $('select[name="row[questionnaire_id]"]').val();
            if (questionnaireId) {
                // 如果已经有问题数据，直接渲染条件
                if (window.questionsData && window.questionsData.length > 0) {
                    Controller.api.renderConditions();
                } else {
                    // 否则加载问题数据
                    Controller.api.loadQuestions(questionnaireId);
                }
            } else {
                // 如果没有选择问卷，但有现有条件，也尝试渲染
                if (window.existingConditions && window.existingConditions.length > 0) {
                    Controller.api.renderConditions();
                }
            }
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
                
                // 问卷选择变化时加载问题
                $(document).on('change', 'select[name="row[questionnaire_id]"]', function() {
                    var questionnaireId = $(this).val();
                    if (questionnaireId) {
                        Controller.api.loadQuestions(questionnaireId);
                    } else {
                        $('#condition-container').empty();
                    }
                });
                
                // 添加条件
                $(document).on('click', '.btn-add-condition', function() {
                    Controller.api.addCondition();
                });
                
                // 删除条件
                $(document).on('click', '.btn-remove-condition', function() {
                    $(this).closest('.condition-item').remove();
                });
                
                // 问题选择变化时加载选项
                $(document).on('change', '.condition-question', function() {
                    var questionId = $(this).val();
                    var $optionSelect = $(this).closest('.condition-item').find('.condition-option');
                    var currentOptionId = $optionSelect.val(); // 保存当前选中的选项ID

                    if (questionId) {
                        Controller.api.loadOptions(questionId, $optionSelect, currentOptionId);
                    } else {
                        $optionSelect.empty().append('<option value="">请选择选项</option>');
                    }
                });
            },
            
            loadQuestions: function(questionnaireId) {
                Fast.api.ajax({
                    url: 'policy/policyrule/getquestions',
                    data: {questionnaire_id: questionnaireId}
                }, function(data, ret) {
                    window.questionsData = data;
                    Controller.api.renderConditions();
                });
            },
            
            loadOptions: function(questionId, $optionSelect, selectedOptionId) {
                if (!window.questionsData) {
                    console.log('没有问题数据');
                    return;
                }

                var question = null;
                for (var i = 0; i < window.questionsData.length; i++) {
                    if (window.questionsData[i].id == questionId) {
                        question = window.questionsData[i];
                        break;
                    }
                }

                $optionSelect.empty().append('<option value="">请选择选项</option>');
                if (question && question.answer_options) {
                    console.log('加载选项:', question.answer_options);
                    for (var j = 0; j < question.answer_options.length; j++) {
                        var option = question.answer_options[j];
                        var selected = selectedOptionId == option.id ? 'selected' : '';
                        $optionSelect.append('<option value="' + option.id + '" ' + selected + '>' + option.title + '</option>');
                    }
                } else {
                    console.log('问题没有选项:', question);
                }
            },
            
            renderConditions: function() {
                var $container = $('#condition-container');
                $container.empty();

                // 如果是编辑模式且有现有条件，渲染现有条件
                if (window.existingConditions && window.existingConditions.length > 0) {
                    console.log('渲染现有条件:', window.existingConditions);
                    for (var i = 0; i < window.existingConditions.length; i++) {
                        Controller.api.addCondition(window.existingConditions[i]);
                    }
                } else {
                    console.log('添加空条件');
                    // 添加一个空条件
                    Controller.api.addCondition();
                }
            },
            
            addCondition: function(condition) {
                condition = condition || {};
                console.log('添加条件:', condition);

                var questionOptions = '<option value="">请选择问题</option>';
                if (window.questionsData) {
                    for (var i = 0; i < window.questionsData.length; i++) {
                        var question = window.questionsData[i];
                        var selected = condition.question_id == question.id ? 'selected' : '';
                        questionOptions += '<option value="' + question.id + '" ' + selected + '>' + question.title + '</option>';
                    }
                }

                var optionOptions = '<option value="">请选择选项</option>';
                if (condition.question_id && window.questionsData) {
                    var question = null;
                    for (var i = 0; i < window.questionsData.length; i++) {
                        if (window.questionsData[i].id == condition.question_id) {
                            question = window.questionsData[i];
                            break;
                        }
                    }
                    if (question && question.answer_options) {
                        for (var j = 0; j < question.answer_options.length; j++) {
                            var option = question.answer_options[j];
                            var selected = condition.answer_option_id == option.id ? 'selected' : '';
                            optionOptions += '<option value="' + option.id + '" ' + selected + '>' + option.title + '</option>';
                        }
                    }
                }

                var typeOptions = '';
                var types = {'must': '必须选择', 'optional': '可选', 'exclude': '排除'};
                for (var type in types) {
                    var selected = condition.condition_type == type ? 'selected' : (type == 'must' && !condition.condition_type ? 'selected' : '');
                    typeOptions += '<option value="' + type + '" ' + selected + '>' + types[type] + '</option>';
                }

                var html = '<div class="condition-item form-group" style="border: 1px solid #ddd; padding: 10px; margin-bottom: 10px; border-radius: 4px;">' +
                    '<div class="row">' +
                        '<div class="col-md-3">' +
                            '<label class="control-label">问题</label>' +
                            '<select name="conditions[' + $('#condition-container .condition-item').length + '][question_id]" class="form-control condition-question">' + questionOptions + '</select>' +
                        '</div>' +
                        '<div class="col-md-3">' +
                            '<label class="control-label">选项</label>' +
                            '<select name="conditions[' + $('#condition-container .condition-item').length + '][answer_option_id]" class="form-control condition-option">' + optionOptions + '</select>' +
                        '</div>' +
                        '<div class="col-md-3">' +
                            '<label class="control-label">条件类型</label>' +
                            '<select name="conditions[' + $('#condition-container .condition-item').length + '][condition_type]" class="form-control">' + typeOptions + '</select>' +
                        '</div>' +
                        '<div class="col-md-3">' +
                            '<label class="control-label">操作</label><br>' +
                            '<button type="button" class="btn btn-danger btn-remove-condition"><i class="fa fa-minus"></i> 删除</button>' +
                        '</div>' +
                    '</div>' +
                '</div>';

                $('#condition-container').append(html);

                // 如果是编辑模式，确保选项正确加载
                if (condition.question_id && condition.answer_option_id) {
                    var $lastCondition = $('#condition-container .condition-item:last');
                    var $questionSelect = $lastCondition.find('.condition-question');
                    var $optionSelect = $lastCondition.find('.condition-option');

                    // 直接加载选项并设置选中值
                    setTimeout(function() {
                        Controller.api.loadOptions(condition.question_id, $optionSelect, condition.answer_option_id);
                    }, 100);
                }
            }
        }
    };
    return Controller;
});
