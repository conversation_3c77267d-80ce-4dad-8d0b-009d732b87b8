-- 为问卷管理添加统计分析功能权限
-- 执行前请确保政策问卷管理菜单已存在

-- 1. 检查问卷管理菜单是否存在
SELECT '=== 检查问卷管理菜单 ===' as info;

SELECT 
    id, 
    pid, 
    name, 
    title, 
    icon, 
    ismenu, 
    weigh, 
    status
FROM fa_auth_rule 
WHERE name = 'policy/questionnaire'
ORDER BY id;

-- 2. 获取问卷管理菜单ID
SET @questionnaire_menu_id = (SELECT id FROM fa_auth_rule WHERE name = 'policy/questionnaire' LIMIT 1);

-- 3. 检查统计分析权限是否已存在
SELECT '=== 检查统计分析权限 ===' as info;

SELECT 
    id, 
    pid, 
    name, 
    title, 
    status
FROM fa_auth_rule 
WHERE name = 'policy/questionnaire/analysis'
ORDER BY id;

-- 4. 添加统计分析权限（如果不存在）
INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', @questionnaire_menu_id, 'policy/questionnaire/analysis', '统计分析', '', '', '问卷统计分析功能', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal');

-- 5. 添加获取统计分析数据的权限
INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', @questionnaire_menu_id, 'policy/questionnaire/getAnalysisData', '获取统计数据', '', '', '获取问卷统计分析数据', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal');

-- 6. 获取新添加的权限ID
SET @analysis_rule_id = (SELECT id FROM fa_auth_rule WHERE name = 'policy/questionnaire/analysis' LIMIT 1);
SET @analysis_data_rule_id = (SELECT id FROM fa_auth_rule WHERE name = 'policy/questionnaire/getAnalysisData' LIMIT 1);

-- 7. 为超级管理员组添加权限
UPDATE `fa_auth_group` 
SET `rules` = CASE 
    WHEN `rules` IS NULL OR `rules` = '' THEN CONCAT(@analysis_rule_id, ',', @analysis_data_rule_id)
    WHEN FIND_IN_SET(@analysis_rule_id, `rules`) = 0 THEN CONCAT(`rules`, ',', @analysis_rule_id, ',', @analysis_data_rule_id)
    ELSE `rules`
END
WHERE `id` = 1;

-- 8. 清理可能的重复逗号
UPDATE `fa_auth_group` 
SET `rules` = TRIM(BOTH ',' FROM REPLACE(REPLACE(REPLACE(`rules`, ',,', ','), ',,', ','), ',,', ','))
WHERE `id` = 1;

-- 9. 显示添加结果
SELECT '=== 统计分析权限添加完成 ===' as info;

SELECT 
    r.id,
    r.pid,
    r.name,
    r.title,
    r.status,
    CASE 
        WHEN r.name = 'policy/questionnaire/analysis' THEN '✓ 统计分析页面权限'
        WHEN r.name = 'policy/questionnaire/getAnalysisData' THEN '✓ 获取统计数据权限'
        ELSE '其他权限'
    END as permission_type
FROM `fa_auth_rule` r 
WHERE r.name IN ('policy/questionnaire/analysis', 'policy/questionnaire/getAnalysisData')
ORDER BY r.id;

-- 10. 验证超级管理员权限
SELECT '=== 超级管理员权限验证 ===' as info;

SELECT 
    g.id as group_id,
    g.name as group_name,
    CASE 
        WHEN FIND_IN_SET(@analysis_rule_id, g.rules) > 0 THEN '✓ 已配置统计分析权限'
        ELSE '❌ 未配置统计分析权限'
    END as permission_status
FROM `fa_auth_group` g 
WHERE g.id = 1;

-- 11. 显示完整的问卷管理权限列表
SELECT '=== 问卷管理完整权限列表 ===' as info;

SELECT 
    r.id,
    r.name,
    r.title,
    CASE 
        WHEN r.name = 'policy/questionnaire' THEN '📂 问卷管理主菜单'
        WHEN r.name LIKE 'policy/questionnaire/%' THEN '⚙️ 功能权限'
        ELSE '其他'
    END as permission_type,
    r.status
FROM `fa_auth_rule` r 
WHERE r.name = 'policy/questionnaire' OR r.name LIKE 'policy/questionnaire/%'
ORDER BY r.name;

-- 12. 显示使用说明
SELECT '=== 使用说明 ===' as info;
SELECT '1. 在问卷管理列表页面，选择一个问卷后点击"统计分析"按钮' as step1;
SELECT '2. 可以通过关键词、用户身份、日期范围等条件筛选数据' as step2;
SELECT '3. 查看用户填写详情和推荐政策信息' as step3;
SELECT '4. 如果看不到按钮，请清除浏览器缓存并重新登录' as step4;

SELECT '=== 权限添加完成 ===' as final_info;
