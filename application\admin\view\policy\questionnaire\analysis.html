<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#one" data-toggle="tab">统计分析</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="row">
                    <div class="col-xs-12">
                        <!-- 问卷基本信息 -->
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h3 class="panel-title">{$row.title} - 统计分析</h3>
                            </div>
                            <div class="panel-body">
                                <!-- 搜索条件 -->
                                <div class="row">
                                    <div class="col-sm-12">
                                        <form class="form-inline" id="searchForm">
                                            <div class="form-group">
                                                <label>关键词：</label>
                                                <input type="text" class="form-control" id="keyword" placeholder="用户昵称或手机号">
                                            </div>
                                            <div class="form-group">
                                                <label>用户身份：</label>
                                                <select class="form-control" id="userRole">
                                                    <option value="">全部</option>
                                                    <option value="city_manager">城市负责人</option>
                                                    <option value="nursing_home_director">养老院长</option>
                                                    <option value="elderly_advisor">养老顾问</option>
                                                    <option value="regular_user">普通用户</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label>开始日期：</label>
                                                <input type="date" class="form-control" id="startDate">
                                            </div>
                                            <div class="form-group">
                                                <label>结束日期：</label>
                                                <input type="date" class="form-control" id="endDate">
                                            </div>
                                            <button type="button" class="btn btn-primary" id="searchBtn">搜索</button>
                                            <button type="button" class="btn btn-default" id="resetBtn">重置</button>
                                        </form>
                                    </div>
                                </div>
                                
                                <hr>
                                
                                <!-- 统计概览 -->
                                <div class="row" id="statisticsOverview">
                                    <div class="col-sm-3">
                                        <div class="info-box bg-aqua">
                                            <span class="info-box-icon"><i class="fa fa-file-text"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">总填写记录</span>
                                                <span class="info-box-number" id="totalRecords">0</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="info-box bg-green">
                                            <span class="info-box-icon"><i class="fa fa-users"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">参与用户数</span>
                                                <span class="info-box-number" id="totalUsers">0</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="info-box bg-yellow">
                                            <span class="info-box-icon"><i class="fa fa-user-circle"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">城市负责人</span>
                                                <span class="info-box-number" id="cityManagerCount">0</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="info-box bg-red">
                                            <span class="info-box-icon"><i class="fa fa-home"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">养老院长</span>
                                                <span class="info-box-number" id="nursingHomeDirectorCount">0</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="info-box bg-purple">
                                            <span class="info-box-icon"><i class="fa fa-heart"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">养老顾问</span>
                                                <span class="info-box-number" id="elderlyAdvisorCount">0</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="info-box bg-gray">
                                            <span class="info-box-icon"><i class="fa fa-user"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">普通用户</span>
                                                <span class="info-box-number" id="regularUserCount">0</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 数据表格 -->
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered" id="analysisTable">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>用户昵称</th>
                                                <th>手机号</th>
                                                <th>用户身份</th>
                                                <th>姓名</th>
                                                <th>年龄</th>
                                                <th>推荐政策数</th>
                                                <th>填写时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="analysisTableBody">
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- 分页 -->
                                <div class="row">
                                    <div class="col-sm-12">
                                        <nav aria-label="Page navigation">
                                            <ul class="pagination" id="pagination">
                                            </ul>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详情模态框 -->
<div class="modal fade" id="detailModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">填写详情</h4>
            </div>
            <div class="modal-body" id="detailModalBody">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var currentPage = 1;
    var pageSize = 20;
    var questionnaireId = {$row.id};
    
    // 加载数据
    function loadData() {
        var params = {
            questionnaire_id: questionnaireId,
            page: currentPage,
            limit: pageSize,
            keyword: $('#keyword').val(),
            user_role: $('#userRole').val(),
            start_date: $('#startDate').val(),
            end_date: $('#endDate').val()
        };
        
        $.ajax({
            url: '{:url("policy/questionnaire/getAnalysisData")}',
            type: 'GET',
            data: params,
            dataType: 'json',
            success: function(response) {
                if (response.code === 1) {
                    updateStatistics(response.data.statistics);
                    updateTable(response.data.list);
                    updatePagination(response.data.total, response.data.page, response.data.limit);
                } else {
                    layer.msg(response.msg || '获取数据失败');
                }
            },
            error: function() {
                layer.msg('网络错误，请重试');
            }
        });
    }
    
    // 更新统计信息
    function updateStatistics(stats) {
        $('#totalRecords').text(stats.total_records);
        $('#totalUsers').text(stats.total_users);
        $('#cityManagerCount').text(stats.role_distribution.city_manager || 0);
        $('#nursingHomeDirectorCount').text(stats.role_distribution.nursing_home_director || 0);
        $('#elderlyAdvisorCount').text(stats.role_distribution.elderly_advisor || 0);
        $('#regularUserCount').text(stats.role_distribution.regular_user || 0);
    }
    
    // 更新表格
    function updateTable(list) {
        var tbody = $('#analysisTableBody');
        tbody.empty();
        
        if (list.length === 0) {
            tbody.append('<tr><td colspan="9" class="text-center">暂无数据</td></tr>');
            return;
        }
        
        $.each(list, function(index, item) {
            var row = '<tr>' +
                '<td>' + item.id + '</td>' +
                '<td>' + (item.user_nickname || '-') + '</td>' +
                '<td>' + (item.user_mobile || '-') + '</td>' +
                '<td>' + item.user_role + '</td>' +
                '<td>' + (item.name || '-') + '</td>' +
                '<td>' + (item.age || '-') + '</td>' +
                '<td>' + (item.recommended_policies ? item.recommended_policies.length : 0) + '</td>' +
                '<td>' + item.createtime_text + '</td>' +
                '<td>' +
                    '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + item.id + '" data-item=\'' + JSON.stringify(item) + '\'>查看详情</button>' +
                '</td>' +
                '</tr>';
            tbody.append(row);
        });
    }
    
    // 更新分页
    function updatePagination(total, page, limit) {
        var totalPages = Math.ceil(total / limit);
        var pagination = $('#pagination');
        pagination.empty();
        
        if (totalPages <= 1) return;
        
        // 上一页
        if (page > 1) {
            pagination.append('<li><a href="#" data-page="' + (page - 1) + '">&laquo;</a></li>');
        }
        
        // 页码
        var start = Math.max(1, page - 2);
        var end = Math.min(totalPages, page + 2);
        
        for (var i = start; i <= end; i++) {
            var activeClass = i === page ? ' class="active"' : '';
            pagination.append('<li' + activeClass + '><a href="#" data-page="' + i + '">' + i + '</a></li>');
        }
        
        // 下一页
        if (page < totalPages) {
            pagination.append('<li><a href="#" data-page="' + (page + 1) + '">&raquo;</a></li>');
        }
    }
    
    // 搜索按钮点击事件
    $('#searchBtn').click(function() {
        currentPage = 1;
        loadData();
    });
    
    // 重置按钮点击事件
    $('#resetBtn').click(function() {
        $('#searchForm')[0].reset();
        currentPage = 1;
        loadData();
    });
    
    // 分页点击事件
    $(document).on('click', '#pagination a', function(e) {
        e.preventDefault();
        var page = $(this).data('page');
        if (page) {
            currentPage = page;
            loadData();
        }
    });
    
    // 查看详情点击事件
    $(document).on('click', '.view-detail', function() {
        var item = JSON.parse($(this).data('item'));
        showDetail(item);
    });
    
    // 显示详情
    function showDetail(item) {
        var html = '<div class="row">' +
            '<div class="col-sm-6">' +
                '<h5>用户信息</h5>' +
                '<p><strong>昵称：</strong>' + (item.user_nickname || '-') + '</p>' +
                '<p><strong>手机号：</strong>' + (item.user_mobile || '-') + '</p>' +
                '<p><strong>身份：</strong>' + item.user_role + '</p>' +
                '<p><strong>姓名：</strong>' + (item.name || '-') + '</p>' +
                '<p><strong>年龄：</strong>' + (item.age || '-') + '</p>' +
                '<p><strong>填写时间：</strong>' + item.createtime_text + '</p>' +
            '</div>' +
            '<div class="col-sm-6">' +
                '<h5>答题详情</h5>';
        
        if (item.answer_details && item.answer_details.length > 0) {
            $.each(item.answer_details, function(index, answer) {
                html += '<div class="panel panel-default">' +
                    '<div class="panel-heading">' + answer.question + '</div>' +
                    '<div class="panel-body">' + answer.selected_options.join(', ') + '</div>' +
                    '</div>';
            });
        } else {
            html += '<p>暂无答题详情</p>';
        }
        
        html += '</div></div>';
        
        if (item.recommended_policies && item.recommended_policies.length > 0) {
            html += '<div class="row"><div class="col-sm-12">' +
                '<h5>推荐政策</h5>';
            
            $.each(item.recommended_policies, function(index, policy) {
                html += '<div class="panel panel-info">' +
                    '<div class="panel-heading">' + policy.title + '</div>' +
                    '<div class="panel-body">' + (policy.summary || '暂无描述') + '</div>' +
                    '</div>';
            });
            
            html += '</div></div>';
        }
        
        $('#detailModalBody').html(html);
        $('#detailModal').modal('show');
    }
    
    // 初始加载
    loadData();
});
</script>
