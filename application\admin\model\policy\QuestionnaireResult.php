<?php

namespace app\admin\model\policy;

use think\Model;

class QuestionnaireResult extends Model
{
    // 表名
    protected $name = 'policy_questionnaire_result';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;

    // 追加属性
    protected $append = [
        'recommended_policies_array',
        'completion_time_text'
    ];

    /**
     * 获取推荐政策数组
     */
    public function getRecommendedPoliciesArrayAttr($value, $data)
    {
        $policies = $value ? $value : (isset($data['recommended_policies']) ? $data['recommended_policies'] : '');
        return $policies ? json_decode($policies, true) : [];
    }

    /**
     * 设置推荐政策
     */
    public function setRecommendedPoliciesAttr($value)
    {
        return is_array($value) ? json_encode($value) : $value;
    }

    /**
     * 获取完成时间文本
     */
    public function getCompletionTimeTextAttr($value, $data)
    {
        $time = $value ? $value : (isset($data['completion_time']) ? $data['completion_time'] : 0);
        
        if ($time < 60) {
            return $time . '秒';
        } elseif ($time < 3600) {
            return floor($time / 60) . '分' . ($time % 60) . '秒';
        } else {
            $hours = floor($time / 3600);
            $minutes = floor(($time % 3600) / 60);
            $seconds = $time % 60;
            return $hours . '时' . $minutes . '分' . $seconds . '秒';
        }
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo('app\common\model\User', 'user_id');
    }

    /**
     * 关联问卷
     */
    public function questionnaire()
    {
        return $this->belongsTo('Questionnaire', 'questionnaire_id');
    }

    /**
     * 生成问卷结果
     */
    public static function generateResult($userId, $questionnaireId, $startTime = null)
    {
        // 获取用户的所有答案
        $userAnswers = UserAnswer::getUserAnswers($userId, $questionnaireId);
        
        if (empty($userAnswers)) {
            return false;
        }

        // 收集所有选择的答案选项ID
        $allOptionIds = [];
        foreach ($userAnswers as $answer) {
            $optionIds = explode(',', $answer->answer_option_ids);
            $allOptionIds = array_merge($allOptionIds, $optionIds);
        }
        $allOptionIds = array_unique($allOptionIds);

        // 获取推荐政策
        $recommendedPolicies = Policy::getRecommendedPolicies($allOptionIds, 10);
        
        // 计算总分
        $totalScore = 0;
        foreach ($recommendedPolicies as $policy) {
            $totalScore += $policy['weight_score'];
        }

        // 计算完成时间
        $completionTime = $startTime ? (time() - $startTime) : 0;

        // 检查是否已有结果记录
        $existResult = self::where([
            'user_id' => $userId,
            'questionnaire_id' => $questionnaireId
        ])->find();

        $data = [
            'user_id' => $userId,
            'questionnaire_id' => $questionnaireId,
            'recommended_policies' => json_encode($recommendedPolicies),
            'total_score' => $totalScore,
            'completion_time' => $completionTime,
            'createtime' => time()
        ];

        if ($existResult) {
            // 更新已有结果
            return $existResult->save($data);
        } else {
            // 创建新结果
            $model = new self();
            return $model->save($data);
        }
    }

    /**
     * 获取用户的问卷结果
     */
    public static function getUserResult($userId, $questionnaireId)
    {
        return self::where([
            'user_id' => $userId,
            'questionnaire_id' => $questionnaireId
        ])->find();
    }

    /**
     * 获取问卷的统计数据
     */
    public static function getQuestionnaireStats($questionnaireId)
    {
        $results = self::where('questionnaire_id', $questionnaireId)->select();
        
        if (empty($results)) {
            return [
                'total_participants' => 0,
                'avg_score' => 0,
                'avg_completion_time' => 0,
                'score_distribution' => [],
                'completion_time_distribution' => []
            ];
        }

        $totalParticipants = count($results);
        $totalScore = array_sum(array_column($results, 'total_score'));
        $totalTime = array_sum(array_column($results, 'completion_time'));

        // 分数分布
        $scoreRanges = [
            '0-20' => 0,
            '21-40' => 0,
            '41-60' => 0,
            '61-80' => 0,
            '81-100' => 0,
            '100+' => 0
        ];

        // 完成时间分布（分钟）
        $timeRanges = [
            '0-1' => 0,
            '2-3' => 0,
            '4-5' => 0,
            '6-10' => 0,
            '11-20' => 0,
            '20+' => 0
        ];

        foreach ($results as $result) {
            // 分数分布统计
            $score = $result['total_score']??'';
            if ($score <= 20) {
                $scoreRanges['0-20']++;
            } elseif ($score <= 40) {
                $scoreRanges['21-40']++;
            } elseif ($score <= 60) {
                $scoreRanges['41-60']++;
            } elseif ($score <= 80) {
                $scoreRanges['61-80']++;
            } elseif ($score <= 100) {
                $scoreRanges['81-100']++;
            } else {
                $scoreRanges['100+']++;
            }

            // 完成时间分布统计（转换为分钟）
            $timeMinutes = ceil($result['completion_time'] / 60);
            if ($timeMinutes <= 1) {
                $timeRanges['0-1']++;
            } elseif ($timeMinutes <= 3) {
                $timeRanges['2-3']++;
            } elseif ($timeMinutes <= 5) {
                $timeRanges['4-5']++;
            } elseif ($timeMinutes <= 10) {
                $timeRanges['6-10']++;
            } elseif ($timeMinutes <= 20) {
                $timeRanges['11-20']++;
            } else {
                $timeRanges['20+']++;
            }
        }

        return [
            'total_participants' => $totalParticipants,
            'avg_score' => $totalParticipants > 0 ? round($totalScore / $totalParticipants, 2) : 0,
            'avg_completion_time' => $totalParticipants > 0 ? round($totalTime / $totalParticipants, 2) : 0,
            'score_distribution' => $scoreRanges,
            'completion_time_distribution' => $timeRanges
        ];
    }

    /**
     * 获取热门政策推荐统计
     */
    public static function getPopularPolicyStats($questionnaireId, $limit = 10)
    {
        $results = self::where('questionnaire_id', $questionnaireId)->column('recommended_policies');
        
        $policyCount = [];
        foreach ($results as $policiesJson) {
            $policies = json_decode($policiesJson, true);
            if (is_array($policies)) {
                foreach ($policies as $policy) {
                    $policyId = $policy['id'];
                    if (!isset($policyCount[$policyId])) {
                        $policyCount[$policyId] = [
                            'policy' => $policy,
                            'count' => 0
                        ];
                    }
                    $policyCount[$policyId]['count']++;
                }
            }
        }

        // 按推荐次数排序
        uasort($policyCount, function($a, $b) {
            return $b['count'] - $a['count'];
        });

        return array_slice($policyCount, 0, $limit);
    }

    /**
     * 删除用户的问卷结果
     */
    public static function deleteUserResult($userId, $questionnaireId)
    {
        return self::where([
            'user_id' => $userId,
            'questionnaire_id' => $questionnaireId
        ])->delete();
    }
}
