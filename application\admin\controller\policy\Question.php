<?php

namespace app\admin\controller\policy;

use app\common\controller\Backend;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 政策问题管理
 *
 * @icon fa fa-question-circle
 */
class Question extends Backend
{
    /**
     * Question模型对象
     * @var \app\admin\model\policy\Question
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\policy\Question;
        $this->view->assign("typeList", $this->model->getTypeList());
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("isRequiredList", $this->model->getIsRequiredList());
        
        // 获取问卷列表
        $questionnaireList = \app\admin\model\policy\Questionnaire::where('status', 'normal')
            ->field('id,title')
            ->select();
        $this->view->assign("questionnaireList", $questionnaireList);
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 手动调整关联查询的搜索和排序字段
            $fixedWhere = $where;
            array_walk_recursive($fixedWhere, function (&$value, $key) {
                if (is_string($value)) {
                    if ($value === 'questionnaire.title') {
                        $value = 'pq.title';
                    }
                }
            });
            if ($sort === 'questionnaire.title') $sort = 'pq.title';

            $list = $this->model
                ->alias('q')
                ->join('policy_questionnaire pq', 'q.questionnaire_id = pq.id', 'LEFT')
                ->field('q.*, pq.title as questionnaire_title')
                ->where($fixedWhere)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $row) {
                // 直接使用查询出的别名，并提供默认值
                $row->questionnaire = [
                    'title' => $row->questionnaire_title ?: '未关联问卷'
                ];
                
                // 统计答案选项数量
                $row->option_count = model('app\admin\model\policy\AnswerOption')->where('question_id', $row->id)->count();
            }
            
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    // 验证问题数据
                    $validation = $this->model->validateQuestionData($params);
                    if ($validation !== true) {
                        throw new ValidateException(implode(', ', $validation));
                    }

                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    // 验证问题数据
                    $validation = $this->model->validateQuestionData($params);
                    if ($validation !== true) {
                        throw new ValidateException(implode(', ', $validation));
                    }

                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 问题统计
     */
    public function statistics($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 获取问题统计信息
        $statistics = $this->model->getQuestionStatistics($ids);

        $this->view->assign("row", $row);
        $this->view->assign("statistics", $statistics);
        
        return $this->view->fetch();
    }

    /**
     * 管理答案选项
     */
    public function options($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if ($row->type === 'inputs') {
            $this->error('自由输入类型的问题不能管理答案选项');
        }

        if ($this->request->isPost()) {
            $options = $this->request->post('options/a');
            if (empty($options)) {
                $this->error('答案选项不能为空');
            }

            Db::startTrans();
            try {
                // 删除原有选项
                \app\admin\model\policy\AnswerOption::where('question_id', $ids)->delete();
                
                // 批量创建新选项
                $result = \app\admin\model\policy\AnswerOption::createBatch($ids, $options);
                
                if (!$result) {
                    throw new \Exception('创建答案选项失败');
                }

                Db::commit();
                $this->success('答案选项保存成功');
            } catch (\Exception $e) {
                Db::rollback();
                $this->error('保存失败：' . $e->getMessage());
            }
        }

        // 获取现有选项
        $options = \app\admin\model\policy\AnswerOption::where('question_id', $ids)
            ->order('sort', 'asc')
            ->select();

        $this->view->assign("row", $row);
        $this->view->assign("options", $options);
        
        return $this->view->fetch();
    }

    /**
     * 复制问题
     */
    public function copy($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if ($this->request->isPost()) {
            $targetQuestionnaireId = $this->request->post('questionnaire_id');
            if (empty($targetQuestionnaireId)) {
                $this->error('请选择目标问卷');
            }

            try {
                $newQuestionId = $this->model->copyToQuestionnaire($ids, $targetQuestionnaireId);
                if ($newQuestionId) {
                    $this->success('问题复制成功', null, ['question_id' => $newQuestionId]);
                } else {
                    $this->error('复制失败');
                }
            } catch (\Exception $e) {
                $this->error('复制失败：' . $e->getMessage());
            }
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
}
