<?php

namespace app\admin\controller\policy;

use app\common\controller\Backend;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 政策问卷管理
 *
 * @icon fa fa-list-alt
 */
class Questionnaire extends Backend
{
    /**
     * Questionnaire模型对象
     * @var \app\admin\model\policy\Questionnaire
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\policy\Questionnaire;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            
            $list = $this->model
                ->with(['questions'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $k=>&$row) {
                 // 添加问题数量
                $row['question_count'] = $row->questions ? count($row->questions) : 0;
                $row->visible(['id','title','question_count','description','cover_image','status','sort','start_time','end_time','createtime']);
                $row->visible(['status_text','start_time_text','end_time_text']);
               
                // 隐藏关联数据的详细信息
                if ($row->questions) {
                    foreach ($row->questions as $question) {
                        $question->visible([]);
                    }
                }
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 问卷统计
     */
    public function statistics($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 获取问卷统计信息
        $statistics = $row->getStatistics($ids);
        
        // 获取问卷结果统计
        $resultStats = \app\admin\model\policy\QuestionnaireResult::getQuestionnaireStats($ids);
        
        // 获取热门政策推荐
        $popularPolicies = \app\admin\model\policy\QuestionnaireResult::getPopularPolicyStats($ids, 10);

        $this->view->assign("row", $row);
        $this->view->assign("statistics", $statistics);
        $this->view->assign("resultStats", $resultStats);
        $this->view->assign("popularPolicies", $popularPolicies);
        
        return $this->view->fetch();
    }

    /**
     * 复制问卷
     */
    public function copy($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if ($this->request->isPost()) {
            $title = $this->request->post('title');
            if (empty($title)) {
                $this->error('问卷标题不能为空');
            }

            Db::startTrans();
            try {
                // 复制问卷
                $newQuestionnaire = $row->toArray();
                unset($newQuestionnaire['id'], $newQuestionnaire['createtime'], $newQuestionnaire['updatetime']);
                $newQuestionnaire['title'] = $title;
                $newQuestionnaire['status'] = 'hidden'; // 默认隐藏状态
                
                $newQuestionnaireModel = new \app\admin\model\policy\Questionnaire();
                $newQuestionnaireModel->save($newQuestionnaire);
                $newQuestionnaireId = $newQuestionnaireModel->id;

                // 复制问题和选项
                $questions = \app\admin\model\policy\Question::where('questionnaire_id', $ids)->select();
                foreach ($questions as $question) {
                    $newQuestionId = $question->copyToQuestionnaire($question->id, $newQuestionnaireId);
                }

                Db::commit();
                $this->success('问卷复制成功', null, ['questionnaire_id' => $newQuestionnaireId]);
            } catch (\Exception $e) {
                Db::rollback();
                $this->error('复制失败：' . $e->getMessage());
            }
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 预览问卷
     */
    public function preview($ids = null)
    {
        $questionnaire = $this->model->getDetailWithQuestions($ids);
        if (!$questionnaire) {
            $this->error(__('No Results were found'));
        }

        $this->view->assign("questionnaire", $questionnaire);
        return $this->view->fetch();
    }

    /**
     * 统计分析页面
     */
    public function analysis($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 获取统计分析数据
     */
    public function getAnalysisData()
    {
        $questionnaireId = $this->request->param('questionnaire_id', 1);
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 20);
        $keyword = $this->request->param('keyword', '');
        $userRole = $this->request->param('user_role', '');
        $startDate = $this->request->param('start_date', '');
        $endDate = $this->request->param('end_date', '');

        // 构建查询条件
        $where = [['questionnaire_id', '=', $questionnaireId]];

        // 日期筛选
        if (!empty($startDate)) {
            $where[] = ['createtime', '>=', strtotime($startDate . ' 00:00:00')];
        }
        if (!empty($endDate)) {
            $where[] = ['createtime', '<=', strtotime($endDate . ' 23:59:59')];
        }

        $query = \app\admin\model\policy\QuestionnaireResult::where($where);

        // 如果有关键词或用户身份筛选，需要关联用户表
        if (!empty($keyword) || !empty($userRole)) {
            $userWhere = [];

            if (!empty($keyword)) {
                $userWhere[] = ['nickname|mobile', 'like', '%' . $keyword . '%'];
            }

            if (!empty($userRole)) {
                switch ($userRole) {
                    case 'city_manager':
                        $userWhere[] = ['is_qydl', '=', 1];
                        break;
                    case 'nursing_home_director':
                        $userWhere[] = ['is_sqdl', '=', 1];
                        break;
                    case 'elderly_advisor':
                        $userWhere[] = ['is_ylgw', '=', 1];
                        break;
                    case 'regular_user':
                        $userWhere[] = ['is_qydl', '=', 0];
                        $userWhere[] = ['is_sqdl', '=', 0];
                        $userWhere[] = ['is_ylgw', '=', 0];
                        break;
                }
            }

            if (!empty($userWhere)) {
                $userIds = Db::name('user')->where($userWhere)->column('id');
                if (!empty($userIds)) {
                    $query = $query->whereIn('user_id', $userIds);
                } else {
                    // 没有匹配的用户，返回空结果
                    return json([
                        'code' => 1,
                        'msg' => '获取成功',
                        'data' => [
                            'list' => [],
                            'total' => 0,
                            'page' => $page,
                            'limit' => $limit,
                            'statistics' => [
                                'total_records' => 0,
                                'total_users' => 0,
                                'role_distribution' => []
                            ]
                        ]
                    ]);
                }
            }
        }

        $records = $query->order('createtime desc')
            ->paginate($limit, false, ['page' => $page]);

        $list = [];
        $userIds = [];

        foreach ($records->items() as $record) {
            $userIds[] = $record->user_id;

            // 获取用户信息
            $userInfo = Db::name('user')
                ->where('id', $record->user_id)
                ->field('id,nickname,mobile,is_qydl,is_sqdl,is_ylgw')
                ->find();

            if (!$userInfo) continue;

            // 确定用户身份
            $userRole = '普通用户';
            if ($userInfo['is_qydl'] == 1) {
                $userRole = '城市负责人';
            } elseif ($userInfo['is_sqdl'] == 1) {
                $userRole = '养老院长';
            } elseif ($userInfo['is_ylgw'] == 1) {
                $userRole = '养老顾问';
            }

            $answers = json_decode($record->answers, true) ?: [];

            // 提取详细答题信息
            $answerDetails = [];
            $name = '';
            $age = '';

            foreach ($answers as $answer) {
                if (isset($answer['question_id'])) {
                    $question = \app\admin\model\policy\Question::where('id', $answer['question_id'])->find();
                    if ($question) {
                        $selectedOptions = [];
                        if (!empty($answer['option_ids']) && is_array($answer['option_ids'])) {
                            foreach ($answer['option_ids'] as $optionId) {
                                $option = \app\admin\model\policy\AnswerOption::where('id', $optionId)->find();
                                if ($option) {
                                    $selectedOptions[] = $option->title;

                                    // 提取姓名和年龄
                                    if (strpos($question->title, '你的名字') !== false || strpos($question->title, '您的姓名') !== false) {
                                        $name = $option->title;
                                    } elseif (strpos($question->title, '您的年龄') !== false || strpos($question->title, '年龄段') !== false) {
                                        $age = $option->title;
                                    }
                                }
                            }
                        }

                        $answerDetails[] = [
                            'question' => $question->title,
                            'question_type' => $question->type,
                            'selected_options' => $selectedOptions
                        ];
                    }
                }
            }

            // 获取推荐政策详情
            $recommendedPolicies = [];
            if (!empty($record->recommended_policies)) {
                $policyIds = explode(',', $record->recommended_policies);
                $policyIds = array_filter($policyIds);
                if (!empty($policyIds)) {
                    $recommendedPolicies = \app\admin\model\policy\Policy::whereIn('id', $policyIds)
                        ->where('status', 'normal')
                        ->field('id,title,summary,category')
                        ->select();
                }
            }

            $list[] = [
                'id' => $record->id,
                'user_id' => $record->user_id,
                'user_nickname' => $userInfo['nickname'],
                'user_mobile' => $userInfo['mobile'],
                'user_role' => $userRole,
                'name' => $name,
                'age' => $age,
                'answer_details' => $answerDetails,
                'recommended_policies' => $recommendedPolicies,
                'completion_time' => $record->completion_time,
                'createtime' => $record->createtime,
                'createtime_text' => date('Y-m-d H:i:s', $record->createtime)
            ];
        }

        // 统计信息
        $totalRecords = $query->count();
        $totalUsers = count(array_unique($userIds));

        // 角色分布统计
        $roleDistribution = [];
        if (!empty($userIds)) {
            $roleStats = Db::name('user')
                ->whereIn('id', array_unique($userIds))
                ->field('
                    SUM(CASE WHEN is_qydl = 1 THEN 1 ELSE 0 END) as city_manager_count,
                    SUM(CASE WHEN is_sqdl = 1 THEN 1 ELSE 0 END) as nursing_home_director_count,
                    SUM(CASE WHEN is_ylgw = 1 THEN 1 ELSE 0 END) as elderly_advisor_count,
                    SUM(CASE WHEN is_qydl = 0 AND is_sqdl = 0 AND is_ylgw = 0 THEN 1 ELSE 0 END) as regular_user_count
                ')
                ->find();

            $roleDistribution = [
                'city_manager' => (int)$roleStats['city_manager_count'],
                'nursing_home_director' => (int)$roleStats['nursing_home_director_count'],
                'elderly_advisor' => (int)$roleStats['elderly_advisor_count'],
                'regular_user' => (int)$roleStats['regular_user_count']
            ];
        }

        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'list' => $list,
                'total' => $records->total(),
                'page' => $page,
                'limit' => $limit,
                'statistics' => [
                    'total_records' => $totalRecords,
                    'total_users' => $totalUsers,
                    'role_distribution' => $roleDistribution
                ]
            ]
        ]);
    }
}
