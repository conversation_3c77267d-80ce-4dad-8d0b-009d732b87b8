<?php

namespace app\api\controller\policy;

use app\common\controller\Api;
use app\admin\model\policy\Questionnaire as QuestionnaireModel;
use app\admin\model\policy\Question;
use app\admin\model\policy\AnswerOption;
use app\admin\model\policy\UserAnswer;
use app\admin\model\policy\QuestionnaireResult;
use app\admin\model\policy\Policy as PolicyModel;
use think\Db;
use think\Exception;

/**
 * 政策问卷API接口
 */
class Questionnaire extends Api
{
    // 无需登录的接口
    protected $noNeedLogin = ['lists', 'detail', 'statistics', 'questions', 'questionDetail', 'options', 'optionDetail'];
    // 无需鉴权的接口
    protected $noNeedRight = ['*'];

    /**
     * @ApiTitle (获取问卷列表)
     * @ApiSummary (获取所有可用的政策问卷列表)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/lists)
     */
    public function lists()
    {
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        $keyword = $this->request->param('keyword', '');
        $status = $this->request->param('status', 'normal');

        $where = [['status', '=', $status]];
        if (!empty($keyword)) {
            $where[] = ['title|description', 'like', '%' . $keyword . '%'];
        }

        $currentTime = time();
        $where[] = ['start_time', '<=', $currentTime];
        $where[] = ['end_time', '>=', $currentTime];

        $list = QuestionnaireModel::where($where)
            ->field('id,title,description,cover_image,total_questions,start_time,end_time,createtime')
            ->order('sort desc, id desc')
            ->paginate($limit, false, ['page' => $page]);

        $this->success('获取成功', [
            'list' => $list->items(),
            'total' => $list->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * @ApiTitle (获取问卷详情)
     * @ApiSummary (获取问卷详细信息，包含所有问题和选项，并按问题描述分组)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/detail)
     */
    public function detail()
    {
        $id = 1; // 默认问卷ID为1
        if (empty($id)) {
            $this->error('问卷ID不能为空');
        }
        
        $questionnaire = QuestionnaireModel::get($id);
        if (!$questionnaire || $questionnaire->status !== 'normal') {
            $this->error('问卷不存在或已下线');
        }

        $currentTime = time();
        if ($questionnaire->start_time > $currentTime || $questionnaire->end_time < $currentTime) {
            $this->error('问卷不在有效期内');
        }

        // 获取所有问题并预加载答案选项
        $questions = Question::with(['answerOptions' => function ($query) {
            $query->where('status', 'normal')->order('sort', 'asc')->field('id, question_id, title, sort');
        }])
            ->where('questionnaire_id', $id)
            ->where('status', 'normal')
            ->order('sort', 'asc')
            ->select();

        // 按 description (问题分类) 对问题进行分组
        $groupedQuestions = [];
        $categoryMap = []; // 用于跟踪每个分类在 $groupedQuestions 中的索引
        foreach ($questions as $question) {
            $category = $question->description ?: '未分类';
            // 如果这个分类还没有在数组中，则添加它
            if (!isset($categoryMap[$category])) {
                $categoryMap[$category] = count($groupedQuestions);
                $groupedQuestions[] = [
                    'name' => $category,
                    'question' => [] // 初始化为空数组
                ];
            }
            // 将当前问题添加到对应分类的 'question' 数组中
            $groupedQuestions[$categoryMap[$category]]['question'][] = $question;
        }

        // 将分组后的问题赋值给问卷对象
        $questionnaire->questions = $groupedQuestions;
        
        $this->success('获取成功', $questionnaire);
    }


    /**
     * @ApiTitle (提交问卷答案)
     * @ApiSummary (提交用户的问卷答案并获取推荐政策)
     * @ApiMethod (POST)
     * @ApiRoute (/api/policy.questionnaire/submit)
     */
    public function submit()
    {
        $questionnaireId = 1;
        $answersJson = $this->request->post('answers');
        $startTime = (int)$this->request->post('start_time', time());
        $sessionId = $this->request->param('session_id', '');

        if (empty($questionnaireId)) $this->error('问卷ID不能为空');
        
        $answers = json_decode(html_entity_decode($answersJson), true);
        if (json_last_error() !== JSON_ERROR_NONE || empty($answers) || !is_array($answers)) {
            $this->error('答案格式不正确');
        }
        
        $questionnaire = QuestionnaireModel::get($questionnaireId);
        if (!$questionnaire || $questionnaire->status !== 'normal') {
            $this->error('问卷不存在或已下线');
        }

        $userId = $this->auth->isLogin() ? $this->auth->id : 0;
        if (!$userId && empty($sessionId)) {
            $sessionId = uniqid('guest_', true);
        }

        Db::startTrans();
        try {
            $where = ['questionnaire_id' => $questionnaireId];
            if ($userId) {
                $where['user_id'] = $userId;
            } else {
                $where['session_id'] = $sessionId;
            }
            // UserAnswer::where($where)->delete();
            // QuestionnaireResult::where($where)->delete();

            foreach ($answers as $answer) {
                //如果用户创建的$answer['question_id']中是必填的,但是未必填,要提示
                $is_requit=db('policy_question')->where('id',$answer['question_id'])->find();
                if($is_requit['is_required'] && empty($answer['option_ids'])){
                     $this->error('['.$is_requit['title'].']必填哦!');
                     break;
                }
                UserAnswer::create([
                    'questionnaire_id' => $questionnaireId,
                    'question_id' => $answer['question_id'],
                    'answer_option_ids' => json_encode($answer['option_ids']),
                    'user_id' => $userId,
                    'session_id' => $sessionId,
                    'createtime' => time()
                ]);
            }
            
            $completionTime = time() - $startTime;

            $result = QuestionnaireResult::create([
                'questionnaire_id' => $questionnaireId,
                'user_id' => $userId,
                'session_id' => $sessionId,
                'answers' => json_encode($answers),
                'completion_time' => $completionTime,
                'matched_rules' => '[]',
                'recommended_policies' => '[]',
                'createtime' => time(),
                'ip' => $this->request->ip()
            ]);

            \think\Log::record('User answers for questionnaire ' . $questionnaireId . ': ' . json_encode($answers), 'info');

            $matchResult = $this->matchPolicies($answers, 1);
          
            $result->matched_rules = implode(',',$matchResult['matched_rule_ids']);
            $result->recommended_policies = implode(',',$matchResult['recommended_policies']);
            $result->save();

            Db::commit();

            $this->success('提交成功', [
                'session_id' => $sessionId,
                'questionnaire_id' => $questionnaireId,
                'completion_time' => $completionTime,
                'matched_rules_count' => count($matchResult['matched_rule_ids']),
                'recommended_policies_count' => count($matchResult['recommended_policies'])
            ]);
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage() . ' at ' . $e->getFile() . ':' . $e->getLine());
        }
    }
    
    /**
     * @ApiTitle (获取问卷结果)
     * @ApiSummary (获取用户的问卷结果和推荐政策)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/result)
     */
    public function getResult()
    {
        
        $sessionId = $this->request->param('session_id', '');

      
        $userId = $this->auth->isLogin() ? $this->auth->id : 0;
        if (!$userId && empty($sessionId)) {
            $this->error('会话ID不能为空');
        }

        $where = ['questionnaire_id' => 1];
        if ($userId) {
            $where['user_id'] = $userId;
        } else {
            $where['session_id'] = $sessionId;
        }

        $result = QuestionnaireResult::where($where)->order('id', 'desc')->find();
        if (!$result) $this->error('未找到问卷结果');

        $recommendedPolicies = [];
        if (!empty($result->recommended_policies)) {
            $policiesData = explode(',',$result->recommended_policies);
            if (is_array($policiesData)) {
             
                    $recommendedPolicies = PolicyModel::whereIn('id', $policiesData)
                        ->where('status', 'normal')
                        ->field('id,title,summary,category,cover_image,view_count,publish_date')
                        ->select();
              
            }
        }
        
        $this->success('获取成功', [
            'questionnaire_id' => $result->questionnaire_id,
            'completion_time' => $result->completion_time,
            'matched_rules' => $result->matched_rules ?  explode(',',$result->matched_rules) : [],
            'recommended_policies' => $recommendedPolicies,
            'submit_time' => $result->createtime
        ]);
    }
    
    private function matchPolicies($answers, $questionnaireId)
    {
        $answerOptions = [];
        foreach ($answers as $answer) {
            if (isset($answer['question_id']) && isset($answer['option_ids']) && is_array($answer['option_ids'])) {
                $answerOptions[$answer['question_id']] = $answer['option_ids'];
            }
        }

        $activeRules = Db::table('fa_policy_rule')
            ->where('questionnaire_id', $questionnaireId)
            ->where('status', 'normal')
            ->order('sort', 'asc')
            ->select();

        if (!$activeRules) {
            return ['matched_rule_ids' => [], 'recommended_policies' => []];
        }

        $activeRuleIds = array_column((array)$activeRules, 'id');
        $conditions = Db::table('fa_policy_rule_condition')->whereIn('rule_id', $activeRuleIds)->select();
            
        $groupedConditions = [];
        foreach ($conditions as $condition) {
            $groupedConditions[$condition['rule_id']][] = $condition;
        }

        $matchedRuleIds = [];
        foreach ($activeRules as $rule) {
            $ruleId = $rule['id'];
            $ruleConditions = $groupedConditions[$ruleId] ?? [];

            if (empty($ruleConditions)) {
                $matchedRuleIds[] = $ruleId;
                continue;
            }

            $mustConditions = array_filter($ruleConditions, fn($c) => $c['condition_type'] === 'must');
            $optionalConditions = array_filter($ruleConditions, fn($c) => $c['condition_type'] === 'optional');
            $excludeConditions = array_filter($ruleConditions, fn($c) => $c['condition_type'] === 'exclude');

            $excludeMet = false;
            foreach ($excludeConditions as $condition) {
                if (in_array($condition['answer_option_id'], $answerOptions[$condition['question_id']] ?? [])) {
                    $excludeMet = true;
                    break;
                }
            }
            if ($excludeMet) continue;

            $mustMet = true;
            foreach ($mustConditions as $condition) {
                if (!in_array($condition['answer_option_id'], $answerOptions[$condition['question_id']] ?? [])) {
                    $mustMet = false;
                    break;
                }
            }
            if (!$mustMet) continue;

            $optionalMet = empty($optionalConditions);
            if (!$optionalMet) {
                foreach ($optionalConditions as $condition) {
                    if (in_array($condition['answer_option_id'], $answerOptions[$condition['question_id']] ?? [])) {
                        $optionalMet = true;
                        break;
                    }
                }
            }
            
            if ($optionalMet) {
                $matchedRuleIds[] = $ruleId;
            }
        }

        if (empty($matchedRuleIds)) {
            return ['matched_rule_ids' => [], 'recommended_policies' => []];
        }

        $policyIdLists = Db::table('fa_policy_rule')->whereIn('id', $matchedRuleIds)->column('policy_ids');
        $allPolicyIds = [];
        foreach ($policyIdLists as $jsonPolicyIds) {
            if (!empty($jsonPolicyIds)) {
                $ids = json_decode($jsonPolicyIds, true);
                if (is_array($ids)) $allPolicyIds = array_merge($allPolicyIds, $ids);
            }
        }
        $uniquePolicyIds = array_unique(array_filter($allPolicyIds));

        if (empty($uniquePolicyIds)) {
            return ['matched_rule_ids' => $matchedRuleIds, 'recommended_policies' => []];
        }
        
        $recommendedPolicies = PolicyModel::whereIn('id', $uniquePolicyIds)
            ->where('status', 'normal')
            ->column('id');

        return [
            'matched_rule_ids' => $matchedRuleIds,
            'recommended_policies' => $recommendedPolicies
        ];
    }

    /**
     * @ApiTitle (获取当前用户填写记录)
     * @ApiSummary (获取当前用户的问卷填写记录列表)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/myRecords)
     */
    public function myRecords()
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        $userId = $this->auth->id;
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);

        // 获取用户填写记录
        $records = QuestionnaireResult::where('user_id', $userId)
            ->order('createtime desc')
            ->paginate($limit, false, ['page' => $page]);

        $list = [];
        foreach ($records->items() as $record) {
            $answers = json_decode($record->answers, true) ?: [];

            // 提取姓名和年龄
            $name = '';
            $age = '';

            foreach ($answers as $answer) {
                if (isset($answer['question_id'])) {
                    // 获取问题信息
                    $question = Question::where('id', $answer['question_id'])->find();
                    if ($question) {
                        if (strpos($question->title, '你的名字') !== false || strpos($question->title, '您的姓名') !== false) {
                            // 获取选项文本作为姓名
                            if (!empty($answer['option_ids']) && is_array($answer['option_ids'])) {
                                $optionId = $answer['option_ids'][0];
                                $option = AnswerOption::where('id', $optionId)->find();
                                if ($option) {
                                    $name = $option->title;
                                }
                            }
                        } elseif (strpos($question->title, '您的年龄') !== false || strpos($question->title, '年龄段') !== false) {
                            // 获取年龄选项
                            if (!empty($answer['option_ids']) && is_array($answer['option_ids'])) {
                                $optionId = $answer['option_ids'][0];
                                $option = AnswerOption::where('id', $optionId)->find();
                                if ($option) {
                                    $age = $option->title;
                                }
                            }
                        }
                    }
                }
            }

            $list[] = [
                'id' => $record->id,
                'display_text' => $name . '-' . $age . '-' . date('Y-m-d', $record->createtime),
                'name' => $name,
                'age' => $age,
                'createtime' => $record->createtime,
                'createtime_text' => date('Y-m-d H:i:s', $record->createtime),
                'recommended_policies_count' => count(explode(',', $record->recommended_policies ?: ''))
            ];
        }

        $this->success('获取成功', [
            'list' => $list,
            'total' => $records->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * @ApiTitle (获取下级用户填写记录)
     * @ApiSummary (养老顾问、养老院长、城市负责人获取下级用户填写记录)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/subordinateRecords)
     */
    public function subordinateRecords()
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        $userId = $this->auth->id;
        $user = $this->auth->getUser();
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        $keyword = $this->request->param('keyword', ''); // 搜索关键词

        // 检查用户角色权限
        if (!($user['is_ylgw'] == 1 || $user['is_sqdl'] == 1 || $user['is_qydl'] == 1)) {
            $this->error('您没有权限查看下级用户记录');
        }

        // 获取下级用户ID列表
        $subordinateUserIds = [];
        if ($user['is_qydl'] == 1) {
            // 城市负责人：获取所有下级用户
            $subordinateUserIds = Db::name('user')
                ->where('parent_id', $userId)
                ->column('id');
        } elseif ($user['is_sqdl'] == 1) {
            // 养老院长：获取所有下级用户
            $subordinateUserIds = Db::name('user')
                ->where('parent_id', $userId)
                ->column('id');
        } elseif ($user['is_ylgw'] == 1) {
            // 养老顾问：获取直接下级用户
            $subordinateUserIds = Db::name('user')
                ->where('parent_id', $userId)
                ->column('id');
        }

        if (empty($subordinateUserIds)) {
            $this->success('获取成功', [
                'list' => [],
                'total' => 0,
                'page' => $page,
                'limit' => $limit
            ]);
        }

        // 构建查询条件
        $where = [['user_id', 'in', $subordinateUserIds]];

        // 获取填写记录
        $query = QuestionnaireResult::where($where);

        if (!empty($keyword)) {
            // 如果有搜索关键词，需要关联用户表搜索
            $userIds = Db::name('user')
                ->where('nickname|mobile', 'like', '%' . $keyword . '%')
                ->whereIn('id', $subordinateUserIds)
                ->column('id');

            if (!empty($userIds)) {
                $query = $query->whereIn('user_id', $userIds);
            } else {
                // 没有匹配的用户，返回空结果
                $this->success('获取成功', [
                    'list' => [],
                    'total' => 0,
                    'page' => $page,
                    'limit' => $limit
                ]);
            }
        }

        $records = $query->order('createtime desc')
            ->paginate($limit, false, ['page' => $page]);

        $list = [];
        foreach ($records->items() as $record) {
            // 获取用户信息
            $userInfo = Db::name('user')
                ->where('id', $record->user_id)
                ->field('id,nickname,mobile,is_qydl,is_sqdl,is_ylgw')
                ->find();

            if (!$userInfo) continue;

            // 确定用户身份
            $userRole = '普通用户';
            if ($userInfo['is_qydl'] == 1) {
                $userRole = '城市负责人';
            } elseif ($userInfo['is_sqdl'] == 1) {
                $userRole = '养老院长';
            } elseif ($userInfo['is_ylgw'] == 1) {
                $userRole = '养老顾问';
            }

            $answers = json_decode($record->answers, true) ?: [];

            // 提取姓名和年龄
            $name = '';
            $age = '';

            foreach ($answers as $answer) {
                if (isset($answer['question_id'])) {
                    $question = Question::where('id', $answer['question_id'])->find();
                    if ($question) {
                        if (strpos($question->title, '你的名字') !== false || strpos($question->title, '您的姓名') !== false) {
                            if (!empty($answer['option_ids']) && is_array($answer['option_ids'])) {
                                $optionId = $answer['option_ids'][0];
                                $option = AnswerOption::where('id', $optionId)->find();
                                if ($option) {
                                    $name = $option->title;
                                }
                            }
                        } elseif (strpos($question->title, '您的年龄') !== false || strpos($question->title, '年龄段') !== false) {
                            if (!empty($answer['option_ids']) && is_array($answer['option_ids'])) {
                                $optionId = $answer['option_ids'][0];
                                $option = AnswerOption::where('id', $optionId)->find();
                                if ($option) {
                                    $age = $option->title;
                                }
                            }
                        }
                    }
                }
            }

            $list[] = [
                'id' => $record->id,
                'user_id' => $record->user_id,
                'user_nickname' => $userInfo['nickname'],
                'user_mobile' => $userInfo['mobile'],
                'user_role' => $userRole,
                'display_text' => $name . '-' . $age . '-' . date('Y-m-d', $record->createtime),
                'name' => $name,
                'age' => $age,
                'createtime' => $record->createtime,
                'createtime_text' => date('Y-m-d H:i:s', $record->createtime),
                'recommended_policies_count' => count(explode(',', $record->recommended_policies ?: ''))
            ];
        }

        $this->success('获取成功', [
            'list' => $list,
            'total' => $records->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * @ApiTitle (问卷统计分析)
     * @ApiSummary (获取所有用户填写的详情和政策推荐信息，用于后台统计分析)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/statisticsAnalysis)
     */
    public function statisticsAnalysis()
    {
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 20);
        $keyword = $this->request->param('keyword', ''); // 搜索关键词
        $userRole = $this->request->param('user_role', ''); // 用户身份筛选
        $startDate = $this->request->param('start_date', ''); // 开始日期
        $endDate = $this->request->param('end_date', ''); // 结束日期

        // 构建查询条件
        $where = [];

        // 日期筛选
        if (!empty($startDate)) {
            $where[] = ['createtime', '>=', strtotime($startDate . ' 00:00:00')];
        }
        if (!empty($endDate)) {
            $where[] = ['createtime', '<=', strtotime($endDate . ' 23:59:59')];
        }

        $query = QuestionnaireResult::where($where);

        // 如果有关键词或用户身份筛选，需要关联用户表
        if (!empty($keyword) || !empty($userRole)) {
            $userWhere = [];

            if (!empty($keyword)) {
                $userWhere[] = ['nickname|mobile', 'like', '%' . $keyword . '%'];
            }

            if (!empty($userRole)) {
                switch ($userRole) {
                    case 'city_manager':
                        $userWhere[] = ['is_qydl', '=', 1];
                        break;
                    case 'nursing_home_director':
                        $userWhere[] = ['is_sqdl', '=', 1];
                        break;
                    case 'elderly_advisor':
                        $userWhere[] = ['is_ylgw', '=', 1];
                        break;
                    case 'regular_user':
                        $userWhere[] = ['is_qydl', '=', 0];
                        $userWhere[] = ['is_sqdl', '=', 0];
                        $userWhere[] = ['is_ylgw', '=', 0];
                        break;
                }
            }

            if (!empty($userWhere)) {
                $userIds = Db::name('user')->where($userWhere)->column('id');
                if (!empty($userIds)) {
                    $query = $query->whereIn('user_id', $userIds);
                } else {
                    // 没有匹配的用户，返回空结果
                    $this->success('获取成功', [
                        'list' => [],
                        'total' => 0,
                        'page' => $page,
                        'limit' => $limit,
                        'statistics' => [
                            'total_records' => 0,
                            'total_users' => 0,
                            'role_distribution' => []
                        ]
                    ]);
                }
            }
        }

        $records = $query->order('createtime desc')
            ->paginate($limit, false, ['page' => $page]);

        $list = [];
        $userIds = [];

        foreach ($records->items() as $record) {
            $userIds[] = $record->user_id;

            // 获取用户信息
            $userInfo = Db::name('user')
                ->where('id', $record->user_id)
                ->field('id,nickname,mobile,is_qydl,is_sqdl,is_ylgw')
                ->find();

            if (!$userInfo) continue;

            // 确定用户身份
            $userRole = '普通用户';
            if ($userInfo['is_qydl'] == 1) {
                $userRole = '城市负责人';
            } elseif ($userInfo['is_sqdl'] == 1) {
                $userRole = '养老院长';
            } elseif ($userInfo['is_ylgw'] == 1) {
                $userRole = '养老顾问';
            }

            $answers = json_decode($record->answers, true) ?: [];

            // 提取详细答题信息
            $answerDetails = [];
            $name = '';
            $age = '';

            foreach ($answers as $answer) {
                if (isset($answer['question_id'])) {
                    $question = Question::where('id', $answer['question_id'])->find();
                    if ($question) {
                        $selectedOptions = [];
                        if (!empty($answer['option_ids']) && is_array($answer['option_ids'])) {
                            foreach ($answer['option_ids'] as $optionId) {
                                $option = AnswerOption::where('id', $optionId)->find();
                                if ($option) {
                                    $selectedOptions[] = $option->title;

                                    // 提取姓名和年龄
                                    if (strpos($question->title, '你的名字') !== false || strpos($question->title, '您的姓名') !== false) {
                                        $name = $option->title;
                                    } elseif (strpos($question->title, '您的年龄') !== false || strpos($question->title, '年龄段') !== false) {
                                        $age = $option->title;
                                    }
                                }
                            }
                        }

                        $answerDetails[] = [
                            'question' => $question->title,
                            'question_type' => $question->type,
                            'selected_options' => $selectedOptions
                        ];
                    }
                }
            }

            // 获取推荐政策详情
            $recommendedPolicies = [];
            if (!empty($record->recommended_policies)) {
                $policyIds = explode(',', $record->recommended_policies);
                $policyIds = array_filter($policyIds);
                if (!empty($policyIds)) {
                    $recommendedPolicies = PolicyModel::whereIn('id', $policyIds)
                        ->where('status', 'normal')
                        ->field('id,title,summary,category')
                        ->select();
                }
            }

            $list[] = [
                'id' => $record->id,
                'user_id' => $record->user_id,
                'user_nickname' => $userInfo['nickname'],
                'user_mobile' => $userInfo['mobile'],
                'user_role' => $userRole,
                'name' => $name,
                'age' => $age,
                'answer_details' => $answerDetails,
                'recommended_policies' => $recommendedPolicies,
                'completion_time' => $record->completion_time,
                'createtime' => $record->createtime,
                'createtime_text' => date('Y-m-d H:i:s', $record->createtime)
            ];
        }

        // 统计信息
        $totalRecords = $query->count();
        $totalUsers = count(array_unique($userIds));

        // 角色分布统计
        $roleDistribution = [];
        if (!empty($userIds)) {
            $roleStats = Db::name('user')
                ->whereIn('id', array_unique($userIds))
                ->field('
                    SUM(CASE WHEN is_qydl = 1 THEN 1 ELSE 0 END) as city_manager_count,
                    SUM(CASE WHEN is_sqdl = 1 THEN 1 ELSE 0 END) as nursing_home_director_count,
                    SUM(CASE WHEN is_ylgw = 1 THEN 1 ELSE 0 END) as elderly_advisor_count,
                    SUM(CASE WHEN is_qydl = 0 AND is_sqdl = 0 AND is_ylgw = 0 THEN 1 ELSE 0 END) as regular_user_count
                ')
                ->find();

            $roleDistribution = [
                'city_manager' => (int)$roleStats['city_manager_count'],
                'nursing_home_director' => (int)$roleStats['nursing_home_director_count'],
                'elderly_advisor' => (int)$roleStats['elderly_advisor_count'],
                'regular_user' => (int)$roleStats['regular_user_count']
            ];
        }

        $this->success('获取成功', [
            'list' => $list,
            'total' => $records->total(),
            'page' => $page,
            'limit' => $limit,
            'statistics' => [
                'total_records' => $totalRecords,
                'total_users' => $totalUsers,
                'role_distribution' => $roleDistribution
            ]
        ]);
    }

}