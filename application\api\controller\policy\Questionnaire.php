<?php

namespace app\api\controller\policy;

use app\common\controller\Api;
use app\admin\model\policy\Questionnaire as QuestionnaireModel;
use app\admin\model\policy\Question;
use app\admin\model\policy\AnswerOption;
use app\admin\model\policy\UserAnswer;
use app\admin\model\policy\QuestionnaireResult;
use app\admin\model\policy\Policy as PolicyModel;
use think\Db;
use think\Exception;

/**
 * 政策问卷API接口
 */
class Questionnaire extends Api
{
    // 无需登录的接口
    protected $noNeedLogin = ['lists', 'detail', 'statistics', 'questions', 'questionDetail', 'options', 'optionDetail'];
    // 无需鉴权的接口
    protected $noNeedRight = ['*'];

    /**
     * @ApiTitle (获取问卷列表)
     * @ApiSummary (获取所有可用的政策问卷列表)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/lists)
     */
    public function lists()
    {
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        $keyword = $this->request->param('keyword', '');
        $status = $this->request->param('status', 'normal');

        $where = [['status', '=', $status]];
        if (!empty($keyword)) {
            $where[] = ['title|description', 'like', '%' . $keyword . '%'];
        }

        $currentTime = time();
        $where[] = ['start_time', '<=', $currentTime];
        $where[] = ['end_time', '>=', $currentTime];

        $list = QuestionnaireModel::where($where)
            ->field('id,title,description,cover_image,total_questions,start_time,end_time,createtime')
            ->order('sort desc, id desc')
            ->paginate($limit, false, ['page' => $page]);

        $this->success('获取成功', [
            'list' => $list->items(),
            'total' => $list->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * @ApiTitle (获取问卷详情)
     * @ApiSummary (获取问卷详细信息，包含所有问题和选项，并按问题描述分组)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/detail)
     */
    public function detail()
    {
        $id = 1; // 默认问卷ID为1
        if (empty($id)) {
            $this->error('问卷ID不能为空');
        }
        
        $questionnaire = QuestionnaireModel::get($id);
        if (!$questionnaire || $questionnaire->status !== 'normal') {
            $this->error('问卷不存在或已下线');
        }

        $currentTime = time();
        if ($questionnaire->start_time > $currentTime || $questionnaire->end_time < $currentTime) {
            $this->error('问卷不在有效期内');
        }

        // 获取所有问题并预加载答案选项
        $questions = Question::with(['answerOptions' => function ($query) {
            $query->where('status', 'normal')->order('sort', 'asc')->field('id, question_id, title, sort');
        }])
            ->where('questionnaire_id', $id)
            ->where('status', 'normal')
            ->order('sort', 'asc')
            ->select();

        // 按 description (问题分类) 对问题进行分组
        $groupedQuestions = [];
        $categoryMap = []; // 用于跟踪每个分类在 $groupedQuestions 中的索引
        foreach ($questions as $question) {
            $category = $question->description ?: '未分类';
            // 如果这个分类还没有在数组中，则添加它
            if (!isset($categoryMap[$category])) {
                $categoryMap[$category] = count($groupedQuestions);
                $groupedQuestions[] = [
                    'name' => $category,
                    'question' => [] // 初始化为空数组
                ];
            }
            // 将当前问题添加到对应分类的 'question' 数组中
            $groupedQuestions[$categoryMap[$category]]['question'][] = $question;
        }

        // 将分组后的问题赋值给问卷对象
        $questionnaire->questions = $groupedQuestions;
        
        $this->success('获取成功', $questionnaire);
    }


    /**
     * @ApiTitle (提交问卷答案)
     * @ApiSummary (提交用户的问卷答案并获取推荐政策)
     * @ApiMethod (POST)
     * @ApiRoute (/api/policy.questionnaire/submit)
     */
    public function submit()
    {
        $questionnaireId = 1;
        $answersJson = $this->request->post('answers');
        $startTime = (int)$this->request->post('start_time', time());
        $sessionId = $this->request->param('session_id', '');

        if (empty($questionnaireId)) $this->error('问卷ID不能为空');
        
        $answers = json_decode(html_entity_decode($answersJson), true);
        if (json_last_error() !== JSON_ERROR_NONE || empty($answers) || !is_array($answers)) {
            $this->error('答案格式不正确');
        }
        
        $questionnaire = QuestionnaireModel::get($questionnaireId);
        if (!$questionnaire || $questionnaire->status !== 'normal') {
            $this->error('问卷不存在或已下线');
        }

        $userId = $this->auth->isLogin() ? $this->auth->id : 0;
        if (!$userId && empty($sessionId)) {
            $sessionId = uniqid('guest_', true);
        }

        Db::startTrans();
        try {
            $where = ['questionnaire_id' => $questionnaireId];
            if ($userId) {
                $where['user_id'] = $userId;
            } else {
                $where['session_id'] = $sessionId;
            }
            // UserAnswer::where($where)->delete();
            // QuestionnaireResult::where($where)->delete();

            foreach ($answers as $answer) {
                //如果用户创建的$answer['question_id']中是必填的,但是未必填,要提示
                $is_requit=db('policy_question')->where('id',$answer['question_id'])->find();
                if($is_requit['is_required'] && empty($answer['option_ids'])){
                     $this->error('['.$is_requit['title'].']必填哦!');
                     break;
                }
                UserAnswer::create([
                    'questionnaire_id' => $questionnaireId,
                    'question_id' => $answer['question_id'],
                    'answer_option_ids' => json_encode($answer['option_ids']),
                    'user_id' => $userId,
                    'session_id' => $sessionId,
                    'createtime' => time()
                ]);
            }
            
            $completionTime = time() - $startTime;

            $result = QuestionnaireResult::create([
                'questionnaire_id' => $questionnaireId,
                'user_id' => $userId,
                'session_id' => $sessionId,
                'answers' => json_encode($answers),
                'completion_time' => $completionTime,
                'matched_rules' => '[]',
                'recommended_policies' => '[]',
                'createtime' => time(),
                'ip' => $this->request->ip()
            ]);

            \think\Log::record('User answers for questionnaire ' . $questionnaireId . ': ' . json_encode($answers), 'info');

            $matchResult = $this->matchPolicies($answers, 1);
          
            $result->matched_rules = implode(',',$matchResult['matched_rule_ids']);
            $result->recommended_policies = implode(',',$matchResult['recommended_policies']);
            $result->save();

            Db::commit();

            $this->success('提交成功', [
                'session_id' => $sessionId,
                'questionnaire_id' => $questionnaireId,
                'completion_time' => $completionTime,
                'matched_rules_count' => count($matchResult['matched_rule_ids']),
                'recommended_policies_count' => count($matchResult['recommended_policies'])
            ]);
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage() . ' at ' . $e->getFile() . ':' . $e->getLine());
        }
    }
    
    /**
     * @ApiTitle (获取问卷结果)
     * @ApiSummary (获取用户的问卷结果和推荐政策)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/result)
     */
    public function getResult()
    {
        
        $sessionId = $this->request->param('session_id', '');

      
        $userId = $this->auth->isLogin() ? $this->auth->id : 0;
        if (!$userId && empty($sessionId)) {
            $this->error('会话ID不能为空');
        }

        $where = ['questionnaire_id' => 1];
        if ($userId) {
            $where['user_id'] = $userId;
        } else {
            $where['session_id'] = $sessionId;
        }

        $result = QuestionnaireResult::where($where)->order('id', 'desc')->find();
        if (!$result) $this->error('未找到问卷结果');

        $recommendedPolicies = [];
        if (!empty($result->recommended_policies)) {
            $policiesData = explode(',',$result->recommended_policies);
            if (is_array($policiesData)) {
             
                    $recommendedPolicies = PolicyModel::whereIn('id', $policiesData)
                        ->where('status', 'normal')
                        ->field('id,title,summary,category,cover_image,view_count,publish_date')
                        ->select();
              
            }
        }
        
        $this->success('获取成功', [
            'questionnaire_id' => $result->questionnaire_id,
            'completion_time' => $result->completion_time,
            'matched_rules' => $result->matched_rules ?  explode(',',$result->matched_rules) : [],
            'recommended_policies' => $recommendedPolicies,
            'submit_time' => $result->createtime
        ]);
    }
    
    private function matchPolicies($answers, $questionnaireId)
    {
        $answerOptions = [];
        foreach ($answers as $answer) {
            if (isset($answer['question_id']) && isset($answer['option_ids']) && is_array($answer['option_ids'])) {
                $answerOptions[$answer['question_id']] = $answer['option_ids'];
            }
        }

        $activeRules = Db::table('fa_policy_rule')
            ->where('questionnaire_id', $questionnaireId)
            ->where('status', 'normal')
            ->order('sort', 'asc')
            ->select();

        if (!$activeRules) {
            return ['matched_rule_ids' => [], 'recommended_policies' => []];
        }

        $activeRuleIds = array_column((array)$activeRules, 'id');
        $conditions = Db::table('fa_policy_rule_condition')->whereIn('rule_id', $activeRuleIds)->select();
            
        $groupedConditions = [];
        foreach ($conditions as $condition) {
            $groupedConditions[$condition['rule_id']][] = $condition;
        }

        $matchedRuleIds = [];
        foreach ($activeRules as $rule) {
            $ruleId = $rule['id'];
            $ruleConditions = $groupedConditions[$ruleId] ?? [];

            if (empty($ruleConditions)) {
                $matchedRuleIds[] = $ruleId;
                continue;
            }

            $mustConditions = array_filter($ruleConditions, fn($c) => $c['condition_type'] === 'must');
            $optionalConditions = array_filter($ruleConditions, fn($c) => $c['condition_type'] === 'optional');
            $excludeConditions = array_filter($ruleConditions, fn($c) => $c['condition_type'] === 'exclude');

            $excludeMet = false;
            foreach ($excludeConditions as $condition) {
                if (in_array($condition['answer_option_id'], $answerOptions[$condition['question_id']] ?? [])) {
                    $excludeMet = true;
                    break;
                }
            }
            if ($excludeMet) continue;

            $mustMet = true;
            foreach ($mustConditions as $condition) {
                if (!in_array($condition['answer_option_id'], $answerOptions[$condition['question_id']] ?? [])) {
                    $mustMet = false;
                    break;
                }
            }
            if (!$mustMet) continue;

            $optionalMet = empty($optionalConditions);
            if (!$optionalMet) {
                foreach ($optionalConditions as $condition) {
                    if (in_array($condition['answer_option_id'], $answerOptions[$condition['question_id']] ?? [])) {
                        $optionalMet = true;
                        break;
                    }
                }
            }
            
            if ($optionalMet) {
                $matchedRuleIds[] = $ruleId;
            }
        }

        if (empty($matchedRuleIds)) {
            return ['matched_rule_ids' => [], 'recommended_policies' => []];
        }

        $policyIdLists = Db::table('fa_policy_rule')->whereIn('id', $matchedRuleIds)->column('policy_ids');
        $allPolicyIds = [];
        foreach ($policyIdLists as $jsonPolicyIds) {
            if (!empty($jsonPolicyIds)) {
                $ids = json_decode($jsonPolicyIds, true);
                if (is_array($ids)) $allPolicyIds = array_merge($allPolicyIds, $ids);
            }
        }
        $uniquePolicyIds = array_unique(array_filter($allPolicyIds));

        if (empty($uniquePolicyIds)) {
            return ['matched_rule_ids' => $matchedRuleIds, 'recommended_policies' => []];
        }
        
        $recommendedPolicies = PolicyModel::whereIn('id', $uniquePolicyIds)
            ->where('status', 'normal')
            ->column('id');

        return [
            'matched_rule_ids' => $matchedRuleIds,
            'recommended_policies' => $recommendedPolicies
        ];
    }


}