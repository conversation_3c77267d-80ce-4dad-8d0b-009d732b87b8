define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'policy/question/index' + location.search,
                    add_url: 'policy/question/add',
                    edit_url: 'policy/question/edit',
                    del_url: 'policy/question/del',
                    multi_url: 'policy/question/multi',
                    options_url: 'policy/question/options',
                    statistics_url: 'policy/question/statistics',
                    copy_url: 'policy/question/copy',
                    table: 'policy_question',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'sort',
                sortOrder: 'asc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: 'ID', sortable: true},
                        {field: 'questionnaire.title', title: '所属问卷', operate: 'LIKE', formatter: Table.api.formatter.search},
                        {field: 'title', title: '问题标题', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'description', title: '问题分类', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {
                            field: 'type',
                            title: '问题类型',
                            searchList: {"single":"单选题","multiple":"多选题","inputs":'自由输入'},
                            formatter: Table.api.formatter.normal
                        },
                        {
                            field: 'is_required',
                            title: '是否必答',
                            searchList: {"0":"否","1":"是"},
                            formatter: Table.api.formatter.normal
                        },
                        {field: 'option_count', title: '选项数量', operate: false},
                        {field: 'sort', title: '排序', operate: false, sortable: true},
                        {
                            field: 'status',
                            title: '状态',
                            searchList: {"normal":"正常","hidden":"隐藏"},
                            formatter: Table.api.formatter.status
                        },
                        {field: 'createtime', title: '创建时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime, sortable: true},
                        {
                            field: 'operate', 
                            title: '操作', 
                            table: table, 
                            events: Table.api.events.operate, 
                            formatter: function (value, row, index) {
                                var table = this.table;
                                // 操作配置
                                var options = table ? table.bootstrapTable('getOptions') : {};
                                // 默认按钮
                                var buttons = $.extend([], this.buttons || []);
                                // 自定义按钮
                                if (row.type !== 'inputs') {
                                    buttons.push({
                                        name: 'options',
                                        icon: 'fa fa-list',
                                        title: '管理答案选项',
                                        classname: 'btn btn-xs btn-primary btn-dialog btn-options',
                                        url: options.extend.options_url
                                    });
                                }
                                buttons.push({
                                    name: 'statistics',
                                    icon: 'fa fa-bar-chart',
                                    title: '问题统计',
                                    classname: 'btn btn-xs btn-info btn-dialog btn-statistics',
                                    url: options.extend.statistics_url
                                });
                                buttons.push({
                                    name: 'copy',
                                    icon: 'fa fa-copy',
                                    title: '复制问题',
                                    classname: 'btn btn-xs btn-success btn-dialog btn-copy',
                                    url: options.extend.copy_url
                                });
                                return Table.api.formatter.operate.call(this, value, row, index, buttons);
                            }
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        options: function () {
            // 选项管理页面逻辑
            Controller.api.bindevent();
            
            // 添加选项
            $(document).on('click', '.btn-add-option', function () {
                var template = $('#option-template').html();
                var index = $('.option-item').length;
                var html = template.replace(/\{index\}/g, index);
                $('#options-container').append(html);
            });

            // 删除选项
            $(document).on('click', '.btn-remove-option', function () {
                $(this).closest('.option-item').remove();
            });
        },
        statistics: function () {
            // 统计页面的初始化逻辑
            Controller.api.bindevent();
        },
        copy: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
