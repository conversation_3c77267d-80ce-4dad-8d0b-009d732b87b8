# 问卷接口测试文档

## 接口列表

### 1. 当前用户填写记录接口
**接口地址**: `GET /api/policy.questionnaire/myRecords`
**功能**: 获取当前用户的问卷填写记录列表
**权限**: 需要登录

**请求参数**:
- `page` (int, 可选): 页码，默认1
- `limit` (int, 可选): 每页数量，默认10

**返回示例**:
```json
{
    "code": 1,
    "msg": "获取成功",
    "time": 1640995200,
    "data": {
        "list": [
            {
                "id": 1,
                "display_text": "张三-25-30岁-2024-01-15",
                "name": "张三",
                "age": "25-30岁",
                "createtime": 1640995200,
                "createtime_text": "2024-01-15 10:30:00",
                "recommended_policies_count": 3
            }
        ],
        "total": 1,
        "page": 1,
        "limit": 10
    }
}
```

### 2. 下级用户填写记录接口
**接口地址**: `GET /api/policy.questionnaire/subordinateRecords`
**功能**: 养老顾问、养老院长、城市负责人获取下级用户填写记录
**权限**: 需要登录，且用户角色为养老顾问、养老院长或城市负责人

**请求参数**:
- `page` (int, 可选): 页码，默认1
- `limit` (int, 可选): 每页数量，默认10
- `keyword` (string, 可选): 搜索关键词（用户昵称或手机号）

**返回示例**:
```json
{
    "code": 1,
    "msg": "获取成功",
    "time": 1640995200,
    "data": {
        "list": [
            {
                "id": 1,
                "user_id": 123,
                "user_nickname": "李四",
                "user_mobile": "13800138000",
                "user_role": "普通用户",
                "display_text": "李四-60-65岁-2024-01-15",
                "name": "李四",
                "age": "60-65岁",
                "createtime": 1640995200,
                "createtime_text": "2024-01-15 14:20:00",
                "recommended_policies_count": 2
            }
        ],
        "total": 1,
        "page": 1,
        "limit": 10
    }
}
```

### 3. 统计分析接口
**接口地址**: `GET /api/policy.questionnaire/statisticsAnalysis`
**功能**: 获取所有用户填写的详情和政策推荐信息，用于后台统计分析
**权限**: 无需登录（建议在生产环境中添加权限控制）

**请求参数**:
- `page` (int, 可选): 页码，默认1
- `limit` (int, 可选): 每页数量，默认20
- `keyword` (string, 可选): 搜索关键词（用户昵称或手机号）
- `user_role` (string, 可选): 用户身份筛选
  - `city_manager`: 城市负责人
  - `nursing_home_director`: 养老院长
  - `elderly_advisor`: 养老顾问
  - `regular_user`: 普通用户
- `start_date` (string, 可选): 开始日期，格式：YYYY-MM-DD
- `end_date` (string, 可选): 结束日期，格式：YYYY-MM-DD

**返回示例**:
```json
{
    "code": 1,
    "msg": "获取成功",
    "time": 1640995200,
    "data": {
        "list": [
            {
                "id": 1,
                "user_id": 123,
                "user_nickname": "王五",
                "user_mobile": "13900139000",
                "user_role": "养老顾问",
                "name": "王五",
                "age": "55-60岁",
                "answer_details": [
                    {
                        "question": "您的年龄段是？",
                        "question_type": "single",
                        "selected_options": ["55-60岁"]
                    },
                    {
                        "question": "您的健康状况如何？",
                        "question_type": "single",
                        "selected_options": ["良好"]
                    }
                ],
                "recommended_policies": [
                    {
                        "id": 1,
                        "title": "老年人高龄津贴政策",
                        "summary": "80周岁以上老年人可申请高龄津贴",
                        "category": "elderly_care"
                    }
                ],
                "completion_time": 120,
                "createtime": 1640995200,
                "createtime_text": "2024-01-15 16:45:00"
            }
        ],
        "total": 1,
        "page": 1,
        "limit": 20,
        "statistics": {
            "total_records": 1,
            "total_users": 1,
            "role_distribution": {
                "city_manager": 0,
                "nursing_home_director": 0,
                "elderly_advisor": 1,
                "regular_user": 0
            }
        }
    }
}
```

## 后台统计分析功能

### 1. 统计分析页面
**访问路径**: 后台 -> 政策问卷 -> 问卷管理 -> 选择问卷 -> 点击"统计分析"按钮

**功能特点**:
- 实时统计概览（总记录数、参与用户数、各角色分布）
- 多条件筛选（关键词、用户身份、日期范围）
- 详细数据表格展示
- 查看用户填写详情和推荐政策
- 分页显示

### 2. 数据获取接口
**接口地址**: `GET /admin/policy/questionnaire/getAnalysisData`
**功能**: 后台获取统计分析数据
**权限**: 需要后台登录权限

**请求参数**: 与前台统计分析接口相同，额外增加：
- `questionnaire_id` (int, 必填): 问卷ID

## 测试步骤

### 1. 测试当前用户填写记录接口
```bash
# 需要先登录获取token
curl -X GET "http://your-domain/api/policy.questionnaire/myRecords?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 测试下级用户填写记录接口
```bash
# 需要养老顾问、养老院长或城市负责人身份
curl -X GET "http://your-domain/api/policy.questionnaire/subordinateRecords?page=1&limit=10&keyword=张三" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 测试统计分析接口
```bash
curl -X GET "http://your-domain/api/policy.questionnaire/statisticsAnalysis?page=1&limit=20&user_role=elderly_advisor&start_date=2024-01-01&end_date=2024-01-31"
```

### 4. 测试后台统计分析
1. 登录后台管理系统
2. 进入"政策问卷" -> "问卷管理"
3. 选择一个问卷
4. 点击"统计分析"按钮
5. 在弹出页面中测试各种筛选条件

## 注意事项

1. **权限控制**: 生产环境中建议为统计分析接口添加适当的权限控制
2. **数据量**: 大量数据时建议增加缓存机制
3. **性能优化**: 可以考虑添加索引优化查询性能
4. **错误处理**: 接口已包含基本的错误处理，但可根据实际需求进一步完善

## 数据库依赖

确保以下数据表存在且有数据：
- `fa_policy_questionnaire_result`: 问卷结果表
- `fa_policy_question`: 问题表
- `fa_policy_answer_option`: 答案选项表
- `fa_user`: 用户表
- `fa_policy`: 政策表

## 权限配置

执行以下SQL文件添加后台权限：
```sql
SOURCE sql/add_questionnaire_analysis_permission.sql;
```
